import type {
  Platform,
  Architecture,
  PlatformFormData,
  ArchitectureFormData,
  BasicStats,
  User,
  LoginRequest,
  RegisterRequest,
  AuthToken
} from './types';
import { getAuthHeader } from './utils/auth';

const API_BASE_URL = '/p-box/api';

class ApiClient {
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;

    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...getAuthHeader(),
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const errorText = await response.text();
      let errorMessage = `HTTP error! status: ${response.status}`;

      try {
        const errorData = JSON.parse(errorText);
        errorMessage = errorData.detail || errorMessage;
      } catch {
        // 如果不是JSON格式，使用默认错误消息
      }

      throw new Error(errorMessage);
    }

    return response.json();
  }

  // 平台相关 API
  async getPlatforms(): Promise<Platform[]> {
    return this.request<Platform[]>('/platforms/');
  }

  async createPlatform(data: PlatformFormData): Promise<Platform> {
    return this.request<Platform>('/platforms/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // 架构相关 API
  async getArchitectures(): Promise<Architecture[]> {
    return this.request<Architecture[]>('/architectures/');
  }

  async createArchitecture(data: ArchitectureFormData): Promise<Architecture> {
    return this.request<Architecture>('/architectures/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // 统计相关 API
  async getBasicStats(): Promise<BasicStats> {
    return this.request<BasicStats>('/stats/');
  }

  // 认证相关 API
  async login(credentials: LoginRequest): Promise<AuthToken> {
    return this.request<AuthToken>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  }

  async register(userData: RegisterRequest): Promise<User> {
    return this.request<User>('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async getCurrentUser(): Promise<User> {
    return this.request<User>('/auth/me');
  }

  async updateProfile(userData: Partial<User>): Promise<User> {
    return this.request<User>('/auth/me', {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  }

  async changePassword(oldPassword: string, newPassword: string): Promise<void> {
    return this.request<void>('/auth/change-password', {
      method: 'POST',
      body: JSON.stringify({
        old_password: oldPassword,
        new_password: newPassword,
      }),
    });
  }
}

export const apiClient = new ApiClient();
