import React, { useState, useEffect } from 'react';
import { Package, Plus, Check, X, Calendar, Tag } from 'lucide-react';

const Releases: React.FC = () => {
  const [loading, setLoading] = useState(false);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">版本管理</h1>
          <p className="mt-1 text-sm text-gray-500">
            管理应用程序发布版本
          </p>
        </div>
        <button
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <Plus className="h-4 w-4 mr-2" />
          发布新版本
        </button>
      </div>

      {/* 版本列表 */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            版本列表
          </h3>
          <div className="text-center py-12">
            <Package className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">暂无版本</h3>
            <p className="mt-1 text-sm text-gray-500">
              点击上方"发布新版本"按钮创建第一个版本
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Releases;
